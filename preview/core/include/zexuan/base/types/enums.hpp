/**
 * @file enums.hpp
 * @brief 枚举类型定义
 * <AUTHOR> from NX framework
 * @date 2025-08-16
 */

#ifndef ZEXUAN_BASE_ENUMS_HPP
#define ZEXUAN_BASE_ENUMS_HPP

namespace zexuan {
namespace base {

// ============================================================================
// 枚举类型定义
// ============================================================================

/// @brief 消息类型枚举
enum class MessageType : int {
    COMMAND = 1,    ///< 命令消息
    RESULT = 2,     ///< 结果消息
    EVENT = 3       ///< 事件消息
};

/// @brief 设备类别枚举
enum class DeviceCategory : int {
    UNKNOWN = 0,        ///< 未知设备
    IED = 1,           ///< 智能电子设备
    SWITCH = 2,        ///< 开关设备
    TRANSFORMER = 3    ///< 变压器设备
};

/// @brief 错误码枚举
enum class ErrorCode : int {
    SUCCESS = 0,                ///< 成功
    INVALID_PARAMETER,          ///< 无效参数
    OBJECT_NOT_FOUND,          ///< 对象未找到
    ALREADY_EXISTS,            ///< 对象已存在
    CALLBACK_NOT_SET,          ///< 回调函数未设置
    MEDIATOR_NOT_AVAILABLE,    ///< 中介者不可用
    OPERATION_FAILED           ///< 操作失败
};

/// @brief 协议转换类型枚举（参考原始 EC_PRO_CVT_TYPE）
enum class ProtocolConvertType {
    UNKNOWN = 0,        // 未知类型
    TO_CALL = 1,        // 召唤命令（单帧处理）
    TO_CTRL = 2,        // 控制命令（多帧处理）
    FROM_LOCAL = 3,     // 本地直接响应
    TO_EVENT = 4        // 事件上送
};

} // namespace base
} // namespace zexuan

#endif // ZEXUAN_BASE_ENUMS_HPP
