/**
 * @file basic_types.hpp
 * @brief 基础类型定义和常量
 * <AUTHOR> from NX framework
 * @date 2025-08-16
 */

#ifndef ZEXUAN_BASE_BASIC_TYPES_HPP
#define ZEXUAN_BASE_BASIC_TYPES_HPP

namespace zexuan {
namespace base {

// ============================================================================
// 基础类型定义
// ============================================================================
using DeviceId = int;
using ObjectId = int;
using EventType = int;

// ============================================================================
// 常量定义
// ============================================================================
constexpr ObjectId INVALID_ID = -1;
constexpr DeviceId ALL_DEVICES = -1;

// ============================================================================
// 标准ID分配规范
// ============================================================================

/// @brief 协议层ID细分
constexpr ObjectId PROTOCOL_GATEWAY_BASE    = 1000;
constexpr ObjectId PROTOCOL_SERVICE_BASE    = 2000;

/// @brief 每个组件的Observer/Subject ID偏移
constexpr ObjectId OBSERVER_OFFSET =  0;   ///< 基础ID作为Observer ID
constexpr ObjectId SUBJECT_OFFSET  =  50;  ///< 基础ID + 50作为Subject ID、

// 协议网关固定ID定义（每个连接独立的Mediator）
constexpr base::ObjectId GATEWAY_OBSERVER_ID = base::PROTOCOL_GATEWAY_BASE + base::OBSERVER_OFFSET;  // 1000
constexpr base::ObjectId GATEWAY_SUBJECT_ID  = base::PROTOCOL_GATEWAY_BASE + base::SUBJECT_OFFSET;   // 1050
// 协议服务固定ID定义（每个连接独立的Mediator）
constexpr base::ObjectId SERVICE_OBSERVER_ID = base::PROTOCOL_SERVICE_BASE + base::OBSERVER_OFFSET;  // 2000
constexpr base::ObjectId SERVICE_SUBJECT_ID  = base::PROTOCOL_SERVICE_BASE + base::SUBJECT_OFFSET;   // 2050

} // namespace base
} // namespace zexuan

#endif // ZEXUAN_BASE_BASIC_TYPES_HPP
