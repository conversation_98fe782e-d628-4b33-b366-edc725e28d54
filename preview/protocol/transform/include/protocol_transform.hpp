#ifndef PROTOCOL_TRANSFORM_HPP
#define PROTOCOL_TRANSFORM_HPP

#include <vector>
#include <memory>
#include <chrono>
#include "zexuan/base/message.hpp"
#include "zexuan/base/base_types.hpp"

namespace zexuan {
namespace protocol {
namespace transform {

// 使用 base 命名空间中的类型定义
using ProtocolConvertType = base::ProtocolConvertType;
using ProtocolFrame = base::ProtocolFrame;
using ProtocolFrameList = base::ProtocolFrameList;

/**
 * @brief 协议转换基类
 * 参考原始的 NXEc60870CvtObj 设计
 * Transform 只处理完整的协议信息，不处理多帧合并
 */
class ProtocolTransform {
public:
    virtual ~ProtocolTransform() = default;

    /**
     * @brief 协议帧转换为通用消息（核心方法）
     * @param frame_list 协议帧列表（输入）
     * @param common_list 通用消息列表（输出）
     * @param result_frames 失败回应帧列表（输出）
     * @return 转换结果，0表示成功
     */
    virtual int ConvertProToCommonMsg(
        const base::ProtocolFrameList& frame_list,
        std::vector<base::CommonMessage>& common_list,
        base::ProtocolFrameList& result_frames) = 0;

    /**
     * @brief 协议帧转换为事件消息
     * @param frame 协议帧（输入）
     * @param event_list 事件消息列表（输出）
     * @return 转换结果，0表示成功
     */
    virtual int ConvertProToEventMsg(
        const base::ProtocolFrame& frame,
        std::vector<base::EventMessage>& event_list) = 0;

    /**
     * @brief 事件消息转换为协议帧
     * @param event_msg 事件消息（输入）
     * @param frame_list 协议帧列表（输出）
     * @return 转换结果，0表示成功
     */
    virtual int ConvertEventMsgToPro(
        const base::EventMessage& event_msg,
        base::ProtocolFrameList& frame_list) = 0;

    /**
     * @brief 通用消息转换为协议帧
     * @param common_msg 通用消息（输入）
     * @param cmd_frames 命令帧列表（输入输出）
     * @param result_frames 结果帧列表（输出）
     * @return 转换结果，0表示成功
     */
    virtual int ConvertCommonMsgToPro(
        const base::CommonMessage& common_msg,
        base::ProtocolFrameList& cmd_frames,
        base::ProtocolFrameList& result_frames) = 0;

    /**
     * @brief 根据协议帧信息获取转换类型（参考原始 GetCvtTypeByProInf）
     * @param frame 协议帧
     * @return 转换类型
     */
    virtual base::ProtocolConvertType GetConvertTypeByFrame(const base::ProtocolFrame& frame) = 0;

    /**
     * @brief 判断目标帧是否为源帧的后续帧（参考原始 IsFollowUpFrame）
     * @param src_frame 源帧
     * @param dst_frame 目标帧
     * @return true-是后续帧 false-不是
     */
    virtual bool IsFollowUpFrame(const base::ProtocolFrame& src_frame, const base::ProtocolFrame& dst_frame) = 0;

};

/**
 * @brief 协议转换工厂
 */
class ProtocolTransformFactory {
public:
    enum class ProtocolType {
        gw104
    };

    /**
     * @brief 创建协议转换器
     * @param type 协议类型
     * @return 转换器实例
     */
    static std::unique_ptr<ProtocolTransform> CreateTransform(ProtocolType type);
};

} // namespace transform
} // namespace protocol
} // namespace zexuan

#endif // PROTOCOL_TRANSFORM_HPP