#include "../../include/gw104/gw104_transform.hpp"
#include "zexuan/base/base_types.hpp"
#include <spdlog/spdlog.h>
#include <cstring>

namespace zexuan {
namespace protocol {
namespace transform {
namespace gw104 {

GW104Transform::GW104Transform() {
    spdlog::info("GW104Transform created");
}

int GW104Transform::ConvertProToCommonMsg(
    const base::ProtocolFrameList& frame_list,
    std::vector<base::CommonMessage>& common_list,
    base::ProtocolFrameList& result_frames) {

    // 循环处理每一帧（参考原始实现）
    for (const auto& frame : frame_list) {
        if (!ValidateFrame(frame.data)) {
            spdlog::error("Invalid protocol frame");
            // 创建错误回应帧
            base::ProtocolFrame error_frame;
            error_frame.type = frame.type;
            error_frame.cot = 47; // 否定确认
            error_frame.asdu_addr = frame.asdu_addr;
            result_frames.push_back(error_frame);
            continue;
        }

        // 尝试解析为标准的 IEC 60870-5-103 消息
        base::Message msg;
        size_t parsed = msg.deserialize(frame.data);

        if (parsed > 0) {
            // 成功解析为IEC103消息，转换为CommonMessage
            base::CommonMessage common_msg;
            common_msg.type = base::MessageType::COMMAND;
            common_msg.source_id = msg.getSource();
            common_msg.target_id = msg.getTarget();
            common_msg.invoke_id = "IEC103_" + std::to_string(msg.getSource()) + "_" + std::to_string(msg.getCot());

            // 将整个IEC103消息作为数据
            common_msg.data.assign(frame.data.begin(), frame.data.end());
            common_list.push_back(common_msg);

            spdlog::debug("Converted IEC103 message: TYP={:02X}, COT={:02X}, SRC={:02X}, TGT={:02X}, FUN={:02X}, INF={:02X}",
                         msg.getTyp(), msg.getCot(), msg.getSource(), msg.getTarget(), msg.getFun(), msg.getInf());
        } else {

            spdlog::error("Converted Failed ?");
        }
    }

    return 0;
}

// 其他方法的空实现
int GW104Transform::ConvertProToEventMsg(
    const base::ProtocolFrame& frame,
    std::vector<base::EventMessage>& event_list) {
    // 暂时空实现
    spdlog::debug("ConvertProToEventMsg not implemented yet");
    return 0;
}

int GW104Transform::ConvertEventMsgToPro(
    const base::EventMessage& event_msg,
    base::ProtocolFrameList& frame_list) {
    // 暂时空实现
    spdlog::debug("ConvertEventMsgToPro not implemented yet");
    return 0;
}

int GW104Transform::ConvertCommonMsgToPro(
    const base::CommonMessage& common_msg,
    base::ProtocolFrameList& cmd_frames,
    base::ProtocolFrameList& result_frames) {
    // 暂时空实现
    spdlog::debug("ConvertCommonMsgToPro not implemented yet");
    return 0;
}

// 新增的多帧处理方法实现

base::ProtocolConvertType GW104Transform::GetConvertTypeByFrame(const base::ProtocolFrame& frame) {
    // 参考原始 GetCvtTypeByProInf 实现
    switch (frame.type) {
        case 1:  // ASDU1 - 单帧测试
            return GetAsdu1ConvertType(frame);
        case 2: // ASDU2 - 多帧测试
            return GetAsdu2ConvertType(frame);
        default:
            return GetDefaultConvertType(frame);
    }
}

bool GW104Transform::IsFollowUpFrame(const base::ProtocolFrame& src_frame, const base::ProtocolFrame& dst_frame) {
    // 参考原始 IsFollowUpFrame 实现
    if ((src_frame.type == dst_frame.type) &&
        (src_frame.asdu_addr == dst_frame.asdu_addr) &&
        (src_frame.addr == dst_frame.addr) &&
        (src_frame.cpu == dst_frame.cpu) &&
        (src_frame.zone == dst_frame.zone) &&
        (src_frame.cot == dst_frame.cot) &&
        (src_frame.fun == dst_frame.fun) &&
        (src_frame.inf == dst_frame.inf) &&
        (src_frame.rii == dst_frame.rii)) {
            return true;
    }
    return false;
}

// 核心辅助方法实现

// 私有方法实现
bool GW104Transform::ValidateFrame(const std::vector<uint8_t>& data) {
    if (data.size() < IEC104_APCI_LENGTH) {
        return false;
    }
    
    if (data[0] != IEC104_START_BYTE) {
        return false;
    }
    
    uint16_t apdu_length = data[1];
    if (data.size() < apdu_length + 2) {
        return false;
    }
    
    return true;
}
// 协议帧类型判断方法实现（参考原始 _GetAsduXXCvtType 系列方法）

base::ProtocolConvertType GW104Transform::GetAsdu1ConvertType(const base::ProtocolFrame& frame) {
    return base::ProtocolConvertType::TO_CALL;
}

base::ProtocolConvertType GW104Transform::GetAsdu2ConvertType(const base::ProtocolFrame& frame) {
    return base::ProtocolConvertType::TO_CTRL;
}

base::ProtocolConvertType GW104Transform::GetDefaultConvertType(const base::ProtocolFrame& frame) {
    return base::ProtocolConvertType::UNKNOWN;
}

} // namespace gw104
} // namespace transform
} // namespace protocol
} // namespace zexuan
