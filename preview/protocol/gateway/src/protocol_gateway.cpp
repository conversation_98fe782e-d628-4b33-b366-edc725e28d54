#include "protocol_gateway.hpp"
#include "protocol_transform.hpp"
#include "protocol_service.hpp"
#include "zexuan/utils/invoke_id_utils.hpp"
#include <spdlog/spdlog.h>
#include <fstream>

namespace zexuan {
namespace protocol {
namespace gateway {

ProtocolGateway::ProtocolGateway(const std::string& config_file_path, std::shared_ptr<base::Mediator> mediator)
    : mediator_(mediator), config_file_path_(config_file_path) {

    // 读取配置文件
    std::ifstream config_file(config_file_path_);
    if (!config_file.is_open()) {
        spdlog::warn("Cannot open config file: {}, using default values", config_file_path_);
        // 使用默认值
        request_timeout_seconds_ = 30;
        thread_sleep_ms_ = 10;
        max_pending_requests_ = 1000;
    } else {
        nlohmann::json config;
        config_file >> config;

        // 读取网关配置
        auto gateway_config = config["protocol"]["gateway"];
        request_timeout_seconds_ = gateway_config.value("request_timeout_seconds", 30);
        thread_sleep_ms_ = gateway_config.value("thread_sleep_ms", 10);
        max_pending_requests_ = gateway_config.value("max_pending_requests", 1000);
        protocol_type_ = gateway_config.value("protocol_type", "gw104");
    }
    
    // 创建Observer - 使用固定ID接收来自Service层的响应和事件
    observer_ = std::make_shared<base::Observer>(base::GATEWAY_OBSERVER_ID, mediator_);

    // 设置Observer的结果回调 - 处理来自Service层的响应
    observer_->SetResultCallback([this](const base::CommonMessage& message) -> base::Result<void> {
        AddServiceResponseToRequest(message);
        return base::Result<void>{};
    });

    // 设置Observer的事件回调 - 处理来自Service层的事件
    observer_->SetEventCallback([this](const base::EventMessage& message) -> base::Result<void> {
        OnServiceEvent(message);
        return base::Result<void>{};
    });

    // 创建Subject - 使用固定ID向Service层发送命令
    subject_ = std::make_shared<base::Subject>(base::GATEWAY_SUBJECT_ID, mediator_);

    // 创建协议转换器
    if (!CreateProtocolTransform()) {
        spdlog::error("Failed to create protocol transform");
        throw std::runtime_error("Failed to create protocol transform");
    }

    // 创建协议服务
    if (!CreateProtocolService()) {
        spdlog::error("Failed to create protocol service");
        throw std::runtime_error("Failed to create protocol service");
    }

    spdlog::info("ProtocolGateway created with Observer ID: {}, Subject ID: {}, protocol: {}",
                 base::GATEWAY_OBSERVER_ID, base::GATEWAY_SUBJECT_ID, protocol_type_);
}



ProtocolGateway::~ProtocolGateway() {
    Stop();
    spdlog::info("ProtocolGateway destroyed");
}

bool ProtocolGateway::Start() {
    if (is_running_.load()) {
        spdlog::warn("Gateway already running");
        return true;
    }

    try {
        // 初始化Observer（包含注册到Mediator）
        auto observer_result = observer_->Init();
        if (!observer_result) {
            spdlog::error("Failed to initialize Observer: {}", static_cast<int>(observer_result.error()));
            return false;
        }

        // 初始化Subject（包含注册到Mediator）
        auto subject_result = subject_->Init();
        if (!subject_result) {
            spdlog::error("Failed to initialize Subject: {}", static_cast<int>(subject_result.error()));
            return false;
        }

        // 启动4个核心线程
        should_stop_.store(false);
        protocol_frame_processor_thread_ = std::thread(&ProtocolGateway::ProtocolFrameProcessorLoop, this);
        local_task_processor_thread_ = std::thread(&ProtocolGateway::LocalTaskProcessorLoop, this);
        response_matcher_thread_ = std::thread(&ProtocolGateway::ResponseMatcherLoop, this);
        event_processor_thread_ = std::thread(&ProtocolGateway::EventProcessorLoop, this);

        is_running_.store(true);
        spdlog::info("Gateway started successfully");
        return true;

    } catch (const std::exception& e) {
        spdlog::error("Failed to start Gateway: {}", e.what());
        return false;
    }
}

void ProtocolGateway::Stop() {
    if (!is_running_.load()) {
        return;
    }

    spdlog::info("Stopping Gateway");
    
    // 停止线程
    should_stop_.store(true);
    
    // 通知所有条件变量
    protocol_cmd_queue_cv_.notify_all();
    local_task_queue_cv_.notify_all();
    event_queue_cv_.notify_all();

    // 等待4个核心线程结束
    if (protocol_frame_processor_thread_.joinable()) protocol_frame_processor_thread_.join();
    if (local_task_processor_thread_.joinable()) local_task_processor_thread_.join();
    if (response_matcher_thread_.joinable()) response_matcher_thread_.join();
    if (event_processor_thread_.joinable()) event_processor_thread_.join();

    // 使用Exit()方法注销Observer和Subject
    if (observer_) observer_->Exit();
    if (subject_) subject_->Exit();

    is_running_.store(false);
    spdlog::info("Gateway stopped");
}

void ProtocolGateway::OnNetworkProtocolData(const std::vector<uint8_t>& protocol_data, uint32_t conn_id) {
    spdlog::debug("Gateway received protocol data from connection {}, length: {}",
                 conn_id, protocol_data.size());

    // 解析协议帧
    base::ProtocolFrame frame;
    if (!ParseProtocolFrame(protocol_data, frame)) {
        spdlog::error("Failed to parse protocol frame from connection {}", conn_id);
        return;
    }

    // 设置连接ID
    frame.frame_id = conn_id;

    // 简单地将协议帧放入原始队列，让 ProtocolFrameProcessorLoop 处理（参考原始 m_ProCmdDeque）
    {
        std::lock_guard<std::mutex> lock(protocol_cmd_queue_mutex_);
        protocol_cmd_queue_.push(frame);
    }
    protocol_cmd_queue_cv_.notify_one();
}

// 新增的协议帧处理方法实现

void ProtocolGateway::HandleSingleFrameCommand(const base::ProtocolFrame& frame) {
    // 单帧召唤命令处理（参考原始 CVT_TO_CALL 处理）
    spdlog::debug("Handling single frame command from connection {}", frame.frame_id);

    base::ProtocolFrameList frame_list = {frame};
    std::vector<base::CommonMessage> common_list;
    base::ProtocolFrameList result_frames;

    int ret = protocol_transform_->ConvertProToCommonMsg(frame_list, common_list, result_frames);
    if (ret != 0) {
        spdlog::error("Failed to convert single frame to common message");
        return;
    }

    // 处理转换失败生成的回应结果
    if (!result_frames.empty()) {
        for (const auto& result_frame : result_frames) {
            if (send_callback_) {
                send_callback_(frame.frame_id, result_frame.data);
            }
        }
        return; // 转换失败，直接返回，不需要等待响应
    }

    // 处理转换成功的召唤命令（参考原始 __AddProCallCmdToAskResDeque）
    if (!common_list.empty()) {
        // 生成包ID（参考原始逻辑）
        static std::atomic<uint32_t> package_id_counter{1};
        uint32_t package_id = package_id_counter.fetch_add(1);

        // 1. 先创建召唤命令的响应匹配包
        if (!AddFrameToResponseRequest(frame, frame.frame_id, false)) {  // false = 召唤命令
            spdlog::error("Failed to add call command to response request");
            return;
        }

        // 2. 为每个通用消息生成invoke_id并建立映射（参考 __SetPackegNxCmdMap）
        std::string request_key = GenerateResponseRequestKey(frame, frame.frame_id);

        for (size_t i = 0; i < common_list.size(); ++i) {
            auto& common_msg = common_list[i];
            common_msg.source_id = frame.frame_id;
            common_msg.target_id = base::SERVICE_SUBJECT_ID;

            // 生成invoke_id格式：package_id/index
            std::string invoke_id = std::to_string(package_id) + "/" + std::to_string(i);
            common_msg.invoke_id = invoke_id;
        }

        // 3. 更新匹配包的invoke_id映射（在发送之前！）
        {
            std::lock_guard<std::mutex> lock(response_requests_mutex_);
            auto it = response_requests_.find(request_key);
            if (it != response_requests_.end()) {
                it->second.commands_sent = true;  // 标记即将发送
                if (!common_list.empty()) {
                    it->second.sent_invoke_id = common_list[0].invoke_id;  // 记录第一个invoke_id
                }
                spdlog::debug("Prepared call command request: key={}, package_id={}", request_key, package_id);
            }
        }

        // 4. 发送所有召唤命令到Service
        for (const auto& common_msg : common_list) {
            if (observer_) {
                auto result = observer_->SendCommand(common_msg, base::SERVICE_SUBJECT_ID);
                if (result) {
                    spdlog::debug("Sent call command to Service: invoke_id={}", common_msg.invoke_id);
                } else {
                    spdlog::error("Failed to send call command to Service: invoke_id={}", common_msg.invoke_id);
                    return;
                }
            } else {
                spdlog::error("Observer not available");
                return;
            }
        }

        spdlog::debug("All call commands sent successfully, package_id={}", package_id);
    }
}

void ProtocolGateway::HandleControlCommand(const base::ProtocolFrame& frame) {
    // 控制命令处理（参考原始 CVT_TO_CTRL 处理）
    spdlog::debug("Handling control command from connection {}", frame.frame_id);

    // 添加到响应处理队列（控制命令）
    if (!AddFrameToResponseRequest(frame, frame.frame_id, true)) {  // true = 控制命令
        spdlog::error("Failed to add frame to response request");
        return;
    }
}

void ProtocolGateway::HandleLocalResponse(const base::ProtocolFrame& frame) {
    // 本地直接响应处理（参考原始 CVT_FROM_LOCAL 处理）
    spdlog::debug("Handling local response frame from connection {}", frame.frame_id);

    // 放入本地任务队列，由 LocalTaskThreadLoop 处理
    {
        std::lock_guard<std::mutex> lock(local_task_queue_mutex_);
        local_task_queue_.push(frame);
    }
    local_task_queue_cv_.notify_one();
}

void ProtocolGateway::HandleEventFrame(const base::ProtocolFrame& frame) {
    // 事件帧处理（参考原始 CVT_TO_EVENT 处理）
    spdlog::debug("Handling event frame from connection {}", frame.frame_id);

    std::vector<base::EventMessage> event_list;
    int ret = protocol_transform_->ConvertProToEventMsg(frame, event_list);
    if (ret != 0) {
        spdlog::error("Failed to convert frame to event message");
        return;
    }

    // 处理事件消息
    for (const auto& event_msg : event_list) {
        // 添加到事件队列
        {
            std::lock_guard<std::mutex> lock(event_queue_mutex_);
            event_queue_.push(event_msg);
        }
        event_queue_cv_.notify_one();
    }
}


void ProtocolGateway::AddServiceResponseToRequest(const base::CommonMessage& result_msg) {
    spdlog::debug("Gateway received response from Service: invoke_id={}, b_lastmsg={}",
                 result_msg.invoke_id, result_msg.b_lastmsg);

    std::lock_guard<std::mutex> lock(response_requests_mutex_);

    // 根据 invoke_id 查找对应的请求
    for (auto& [request_key, request] : response_requests_) {
        if (request.sent_invoke_id == result_msg.invoke_id) {
            // 找到对应的请求，添加响应
            request.nx_response_list.push_back(result_msg);

            spdlog::debug("Added response to request: key={}, invoke_id={}, total_responses={}, b_lastmsg={}",
                         request_key, result_msg.invoke_id, request.nx_response_list.size(), result_msg.b_lastmsg);

            return;
        }
    }

    spdlog::warn("No matching request found for response: invoke_id={}", result_msg.invoke_id);
}

void ProtocolGateway::OnServiceEvent(const base::EventMessage& event_msg) {
    // 添加到事件队列
    {
        std::lock_guard<std::mutex> lock(event_queue_mutex_);
        event_queue_.push(event_msg);
        event_queue_cv_.notify_one();
    }
}

void ProtocolGateway::SetSendCallback(std::function<bool(uint32_t, const std::vector<uint8_t>&)> callback) {
    send_callback_ = callback;
}



// 线程方法实现
void ProtocolGateway::ProtocolFrameProcessorLoop() {
    spdlog::info("协议帧处理线程启动 (参考原始 __DoCallAskLoop)");

    while (!should_stop_.load()) {
        base::ProtocolFrame frame;

        // 等待原始协议帧（参考原始 m_ProCmdDeque）
        {
            std::unique_lock<std::mutex> lock(protocol_cmd_queue_mutex_);
            protocol_cmd_queue_cv_.wait(lock, [this] {
                return !protocol_cmd_queue_.empty() || should_stop_.load();
            });

            if (should_stop_.load()) break;

            if (!protocol_cmd_queue_.empty()) {
                frame = protocol_cmd_queue_.front();
                protocol_cmd_queue_.pop();
            } else {
                continue;
            }
        }

        // 使用Transform判断协议帧类型（参考原始 GetCvtTypeByProInf）
        if (!protocol_transform_) {
            spdlog::error("Protocol transform not available");
            continue;
        }

        base::ProtocolConvertType convert_type = protocol_transform_->GetConvertTypeByFrame(frame);

        switch (convert_type) {
            case base::ProtocolConvertType::TO_CALL:
                // 单帧召唤命令，直接转换并放入Command队列
                HandleSingleFrameCommand(frame);
                break;

            case base::ProtocolConvertType::TO_CTRL:
                // 控制命令（可能单帧或多帧），加入响应处理队列
                HandleControlCommand(frame);
                break;

            case base::ProtocolConvertType::FROM_LOCAL:
                // 本地直接响应，放入本地任务队列
                HandleLocalResponse(frame);
                break;

            case base::ProtocolConvertType::TO_EVENT:
                // 事件帧，直接转换并放入Event队列
                HandleEventFrame(frame);
                break;

            default:
                spdlog::error("Unknown protocol convert type for frame from connection {}", frame.frame_id);
                break;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(thread_sleep_ms_));
    }

    spdlog::info("协议帧处理线程停止");
}

void ProtocolGateway::LocalTaskProcessorLoop() {
    spdlog::info("LocalTaskProcessorLoop Started");

    while (!should_stop_.load()) {
        base::ProtocolFrame frame;
        
        // 等待本地任务（参考原始线程池）
        {
            std::unique_lock<std::mutex> lock(local_task_queue_mutex_);
            local_task_queue_cv_.wait(lock, [this] {
                return !local_task_queue_.empty() || should_stop_.load();
            });

            if (should_stop_.load()) break;

            if (!local_task_queue_.empty()) {
                frame = local_task_queue_.front();
                local_task_queue_.pop();
            } else {
                continue;
            }
        }

        try {
            // 处理本地响应任务（参考原始 __GetInfoFromLocal）
            spdlog::debug("Processing local task for frame from connection {}", frame.frame_id);

            // 这里可以直接生成响应，不需要转发给Service
            // 暂时简单实现：发送确认帧
            if (send_callback_) {
                // 创建简单的确认响应
                std::vector<uint8_t> response_data = {0x68, 0x04, 0x07, 0x00, 0x00, 0x00}; // 简单的确认帧
                send_callback_(frame.frame_id, response_data);
                spdlog::debug("Sent local response for connection {}", frame.frame_id);
            }

        } catch (const std::exception& e) {
            spdlog::error("Exception in local task thread: {}", e.what());
        }
    }
    
    spdlog::info("LocalTaskProcessorLoop Stoped");
}

void ProtocolGateway::ResponseMatcherLoop() {
    spdlog::info("ResponseMatcherLoop Strated");

    while (!should_stop_.load()) {
        try {
            // 处理响应请求（参考原始 __CallResponseHandle）
            {
                std::lock_guard<std::mutex> lock(response_requests_mutex_);

                auto it = response_requests_.begin();
                while (it != response_requests_.end()) {
                    ResponseRequest& request = it->second;

                    // 检查超时（参考原始超时处理）
                    auto now = std::chrono::system_clock::now();
                    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - request.create_time);

                    if (elapsed.count() > request_timeout_seconds_) {
                        spdlog::warn("Response request timeout, removing: key={}", it->first);
                        it = response_requests_.erase(it);
                        continue;
                    }

                    // 控制命令的转换处理：为控制命令、收到完整帧序列且还没有发送给Service
                    // 判断是否收到完整帧序列：收到最后一帧 或 收到的帧数达到期望帧数
                    bool frames_complete = request.received_last_cmd ||
                                         (request.received_frame_count >= request.expected_frame_count);

                    if (request.is_ctrl_cmd && frames_complete && !request.commands_sent) {
                        spdlog::debug("Processing complete control command: key={}, frames={}/{}, last_frame={}",
                                     it->first, request.received_frame_count, request.expected_frame_count, request.received_last_cmd);

                        if (!ProcessControlCommand(it->first, request)) {
                            // 处理失败，删除该请求
                            it = response_requests_.erase(it);
                            spdlog::error("Control command processing failed, removed request");
                            continue;
                        }
                        request.commands_sent = true;
                    }

                    // 结果的转换处理（检查是否收到所有响应）
                    if (request.commands_sent && CheckAndProcessCompleteResponse(it->first, request)) {
                        // 处理完成，删除该请求
                        it = response_requests_.erase(it);
                        continue;
                    }

                    ++it;
                }
            }

            // TODO: 可以在这里添加响应请求的超时检查

            std::this_thread::sleep_for(std::chrono::milliseconds(thread_sleep_ms_));

        } catch (const std::exception& e) {
            spdlog::error("Exception in response request matcher: {}", e.what());
        }
    }

    spdlog::info("ResponseMatcherLoop Stoped");
}

void ProtocolGateway::EventProcessorLoop() {
    spdlog::info("EventProcessorLoop Started");

    while (!should_stop_.load()) {
        base::EventMessage event_message;

        // 等待事件
        {
            std::unique_lock<std::mutex> lock(event_queue_mutex_);
            event_queue_cv_.wait(lock, [this] { return !event_queue_.empty() || should_stop_.load(); });

            if (should_stop_.load()) break;

            if (!event_queue_.empty()) {
                event_message = event_queue_.front();
                event_queue_.pop();
            } else {
                continue;
            }
        }

        try {
            // 转换事件为协议字节流并发送
            std::vector<uint8_t> protocol_event = ConvertEventToProtocolBytes(event_message);

            if (send_callback_) {
                uint32_t conn_id = static_cast<uint32_t>(event_message.source_id);
                bool result = send_callback_(conn_id, protocol_event);

                if (result) {
                    spdlog::debug("Sent event to network, type: {}", event_message.event_type);
                } else {
                    spdlog::error("Failed to send event to network, type: {}", event_message.event_type);
                }
            }

        } catch (const std::exception& e) {
            spdlog::error("Exception in event processor: {}", e.what());
        }
    }

    spdlog::info("EventProcessorLoop Stoped");
}







std::vector<uint8_t> ProtocolGateway::ConvertEventToProtocolBytes(const base::EventMessage& event) {
    std::string protocol_str = "EVENT:type=" + std::to_string(event.event_type) + ";desc=" + event.description + ";";
    return std::vector<uint8_t>(protocol_str.begin(), protocol_str.end());
}

// 组件创建方法
bool ProtocolGateway::CreateProtocolTransform() {
    transform::ProtocolTransformFactory::ProtocolType type;

    if (protocol_type_ == "gw104") {
        type = transform::ProtocolTransformFactory::ProtocolType::gw104;
    } else {
        spdlog::error("Unsupported protocol type: {}", protocol_type_);
        return false;
    }

    protocol_transform_ = transform::ProtocolTransformFactory::CreateTransform(type);
    if (!protocol_transform_) {
        spdlog::error("Failed to create transform for protocol: {}", protocol_type_);
        return false;
    }

    spdlog::info("Created protocol transform: {}", protocol_type_);
    return true;
}

bool ProtocolGateway::CreateProtocolService() {
    // 创建Service，使用标准固定ID
    protocol_service_ = std::make_unique<service::ProtocolService>(mediator_);
    if (!protocol_service_) {
        spdlog::error("Failed to create protocol service");
        return false;
    }

    // 将Transform注入到Service中
    // 注意：这里需要创建Transform的副本，因为Gateway也需要使用Transform
    auto service_transform = transform::ProtocolTransformFactory::CreateTransform(
        transform::ProtocolTransformFactory::ProtocolType::gw104);
    protocol_service_->SetProtocolTransform(std::move(service_transform));

    // 初始化并启动Service
    if (!protocol_service_->Initialize()) {
        spdlog::error("Failed to initialize protocol service");
        return false;
    }

    if (!protocol_service_->Start()) {
        spdlog::error("Failed to start protocol service");
        return false;
    }

    spdlog::info("Created and started protocol service");
    return true;
}

// 协议帧解析方法
bool ProtocolGateway::ParseProtocolFrame(const std::vector<uint8_t>& data, base::ProtocolFrame& frame) {
    if (data.empty()) {
        return false;
    }

    // 只使用 Message 反序列化，失败就丢弃
    base::Message msg;
    size_t parsed = msg.deserialize(data);

    if (parsed > 0) {
        // 成功解析为协议消息
        frame.data = data;
        frame.timestamp = std::chrono::system_clock::now();
        frame.type = msg.getTyp();
        frame.cot = msg.getCot();
        frame.asdu_addr = msg.getTarget();  // 使用目标地址作为ASDU地址
        frame.vsq = msg.getVsq();

        // 根据VSQ判断是否为最后一帧（参考103协议标准）
        // VSQ的低7位表示信息对象数量，第8位(0x80)表示是否有后续帧
        // 如果第8位为1，表示有后续帧；为0表示这是最后一帧
        frame.is_last_frame = (msg.getVsq() & 0x80) == 0;  // 第8位为0表示最后一帧
        frame.frame_id = 0;  // 这里会在 OnNetworkProtocolData 中设置为实际的连接ID

        // 填充其他协议字段（用于多帧匹配）
        frame.addr = msg.getSource();
        frame.cpu = 0;  // IEC103中通常不使用
        frame.zone = 0; // IEC103中通常不使用
        frame.fun = msg.getFun();
        frame.inf = msg.getInf();
        frame.rii = 0;  // IEC103中通常不使用
        frame.group = 0; // 需要从数据中解析，暂时设为0
        frame.kod = 0;   // 需要从数据中解析，暂时设为0

        spdlog::debug("Parsed protocol frame: TYP={:02X}, VSQ={:02X}, COT={:02X}, SRC={:02X}, TGT={:02X}, FUN={:02X}, INF={:02X}, is_last={}, data_len={}",
                     msg.getTyp(), msg.getVsq(), msg.getCot(), msg.getSource(), msg.getTarget(), msg.getFun(), msg.getInf(), frame.is_last_frame, data.size());

        return true;
    } else {
        // 解析失败，丢弃数据
        spdlog::warn("Failed to parse protocol frame, discarding data of length {}", data.size());
        return false;
    }
}

bool ProtocolGateway::IsLastFrame(const base::ProtocolFrame& frame) {
    return frame.is_last_frame;
}





// 响应处理辅助方法实现

bool ProtocolGateway::AddFrameToResponseRequest(const base::ProtocolFrame& frame, uint32_t conn_id, bool is_ctrl_cmd) {
    std::lock_guard<std::mutex> lock(response_requests_mutex_);

    std::string request_key = GenerateResponseRequestKey(frame, conn_id);

    // 参考原始 __AddProCtrlCmdToAskResDeque 的逻辑

    // 查找是否已存在相关的响应请求
    auto it = response_requests_.find(request_key);
    if (it != response_requests_.end()) {
        // 检查是否为后续帧
        bool is_follow_up = false;
        for (const auto& existing_frame : it->second.frame_list) {
            if (protocol_transform_->IsFollowUpFrame(existing_frame, frame)) {
                is_follow_up = true;
                break;
            }
        }

        if (is_follow_up) {
            // 添加到现有请求（参考原始逻辑）
            it->second.frame_list.push_back(frame);
            it->second.received_frame_count++;

            // 更新最后一帧标志
            it->second.received_last_cmd = frame.is_last_frame;

            // 如果这是最后一帧，更新期望帧数为当前收到的帧数
            if (frame.is_last_frame) {
                it->second.expected_frame_count = it->second.received_frame_count;
            }

            spdlog::debug("Added follow-up frame to control command: key={}, received_frames={}/{}, is_last={}",
                         request_key, it->second.received_frame_count, it->second.expected_frame_count, frame.is_last_frame);

            return true;
        }

        // 检查是否为预校与执行命令的匹配
        bool is_updated = false;
        for (auto& existing_frame : it->second.frame_list) {
            if (protocol_transform_->UpdateFrameBySrcFrame(existing_frame, const_cast<base::ProtocolFrame&>(frame))) {
                is_updated = true;
                spdlog::debug("Updated control command with execution frame: key={}", request_key);
                break;
            }
        }

        if (is_updated) {
            return true;
        }

    }

    // 创建新的响应请求（参考原始 ASK_RES_MATCH_PACKAGE 创建逻辑）
    ResponseRequest new_request;
    new_request.frame_list.push_back(frame);
    new_request.is_ctrl_cmd = is_ctrl_cmd;  // 使用传入的命令类型
    new_request.received_last_cmd = frame.is_last_frame;  // 使用帧的bLast字段
    new_request.conn_id = conn_id;
    new_request.create_time = std::chrono::system_clock::now();

    // 根据VSQ设置帧计数信息（参考103协议标准）
    // VSQ的低7位表示信息对象数量，用于计算期望的帧数
    uint8_t info_obj_count = frame.vsq & 0x7F;  // 取低7位
    new_request.expected_frame_count = CalculateExpectedFrameCount(frame.type, info_obj_count, frame.is_last_frame);
    new_request.received_frame_count = 1;

    response_requests_[request_key] = new_request;

    spdlog::debug("Created new {} request: key={}, is_last={}",
                 is_ctrl_cmd ? "control command" : "call command", request_key, frame.is_last_frame);

    // 让 ResponseMatcherLoop 处理
    // 响应匹配线程会检查 received_last_cmd 并调用 ProcessControlCommand

    return true;
}



std::string ProtocolGateway::GenerateResponseRequestKey(const base::ProtocolFrame& frame, uint32_t conn_id) {
    // 生成响应请求的唯一键（参考原始匹配逻辑）
    return std::to_string(conn_id) + "_" +
           std::to_string(frame.type) + "_" +
           std::to_string(frame.asdu_addr) + "_" +
           std::to_string(frame.addr) + "_" +
           std::to_string(frame.cpu) + "_" +
           std::to_string(frame.zone) + "_" +
           std::to_string(frame.cot) + "_" +
           std::to_string(frame.fun);
}



// RequestMatcherThreadLoop 中使用的方法实现

bool ProtocolGateway::ProcessControlCommand(const std::string& request_key, ResponseRequest& request) {
    // 参考原始 __PackegCtrlCmdHandle
    spdlog::debug("Processing control command: key={}", request_key);

    std::vector<base::CommonMessage> common_list;
    base::ProtocolFrameList result_frames;

    int ret = protocol_transform_->ConvertProToCommonMsg(request.frame_list, common_list, result_frames);
    if (ret != 0) {
        spdlog::error("Failed to convert control command");

        // 发送错误回应
        for (const auto& result_frame : result_frames) {
            if (send_callback_) {
                send_callback_(request.conn_id, result_frame.data);
            }
        }
        return false;
    }

    // 转换成功，发送给Service
    // 注意：我们假设只有一个CommonMessage（帧合并为一个命令）
    if (common_list.size() != 1) {
        spdlog::error("Expected exactly 1 CommonMessage, got {}", common_list.size());
        return false;
    }

    auto& common_msg = common_list[0];
    common_msg.source_id = request.conn_id;
    common_msg.target_id = base::SERVICE_SUBJECT_ID;

    // 自己生成invoke_id，确保匹配安全
    std::string invoke_id = utils::invoke_id::Generate(base::GATEWAY_OBSERVER_ID);
    common_msg.invoke_id = invoke_id;

    // 发送给Service
    if (observer_) {
        auto result = observer_->SendCommand(common_msg, base::SERVICE_SUBJECT_ID);
        if (result) {
            // 记录我们生成的 invoke_id
            request.sent_invoke_id = invoke_id;
            spdlog::debug("Sent control command to Service: invoke_id={}", invoke_id);
        } else {
            spdlog::error("Failed to send control command to Service");
            return false;
        }
    } else {
        spdlog::error("Observer not available");
        return false;
    }

    return true;
}

bool ProtocolGateway::CheckAndProcessCompleteResponse(const std::string& request_key, ResponseRequest& request) {
    // 简化的响应检查逻辑
    if (request.nx_response_list.empty()) {
        return false; // 没有收到任何响应
    }

    // 检查最后一个响应是否为最后一帧
    bool last_frame_received = request.nx_response_list.back().b_lastmsg;

    if (last_frame_received) {
        spdlog::debug("All responses received for request: key={}, total_responses={}",
                     request_key, request.nx_response_list.size());

        // 转换响应并发送
        ProcessCompleteResponse(request_key, request);
        return true;
    }

    return false;
}

void ProtocolGateway::ProcessCompleteResponse(const std::string& request_key, ResponseRequest& request) {
    spdlog::debug("Processing complete response: key={}, responses={}",
                 request_key, request.nx_response_list.size());

    // 按顺序发送所有响应帧
    for (const auto& response : request.nx_response_list) {
        if (send_callback_ && !response.data.empty()) {
            uint32_t conn_id = static_cast<uint32_t>(response.target_id);

            // 检查数据是否是有效的IEC103消息
            base::Message test_msg;
            size_t parsed = test_msg.deserialize(response.data);

            if (parsed > 0) {
                // 是有效的IEC103消息，直接发送
                send_callback_(conn_id, response.data);
                spdlog::debug("Sent response to connection {}: {} bytes, b_lastmsg={}",
                             conn_id, response.data.size(), response.b_lastmsg);

                // 显示发送的消息详情
                spdlog::debug("Response message: TYP={:02X}, COT={:02X}, SRC={:02X}, TGT={:02X}, FUN={:02X}, INF={:02X}",
                             test_msg.getTyp(), test_msg.getCot(), test_msg.getSource(), test_msg.getTarget(),
                             test_msg.getFun(), test_msg.getInf());
            } else {
                // 不是IEC103消息，直接发送原始数据
                send_callback_(conn_id, response.data);
                spdlog::debug("Sent raw response to connection {}: {} bytes", conn_id, response.data.size());
            }
        }
    }

    spdlog::info("Completed response processing: key={}, sent {} frames",
                request_key, request.nx_response_list.size());
}

uint8_t ProtocolGateway::CalculateExpectedFrameCount(uint8_t type, uint8_t info_obj_count, bool is_last_frame) {
    // 根据IEC 60870-5-103协议标准计算期望的帧数
    // 参考原始实现中的多帧处理逻辑

    if (is_last_frame) {
        // 如果当前帧已经是最后一帧，则期望帧数就是1
        return 1;
    }

    // 根据消息类型和信息对象数量估算帧数
    switch (type) {
        case 1:   // ASDU1 - 单点信息
        case 2:   // ASDU2 - 双点信息
        case 3:   // ASDU3 - 步位置信息
        case 4:   // ASDU4 - 带时标的单点信息
        case 5:   // ASDU5 - 带时标的双点信息
            // 对于这些类型，通常每帧包含多个信息对象
            // 如果信息对象数量较多，可能需要多帧传输
            if (info_obj_count > 50) {
                return (info_obj_count + 49) / 50;  // 每帧最多50个对象，向上取整
            }
            return 1;

        case 10:  // ASDU10 - 总召唤
        case 21:  // ASDU21 - 通用分类数据
            // 这些类型可能产生大量响应数据，需要多帧传输
            // 根据VSQ的第8位判断是否有后续帧
            return info_obj_count > 0 ? info_obj_count : 1;

        default:
            // 对于其他类型，默认为单帧
            return 1;
    }
}

} // namespace gateway
} // namespace protocol
} // namespace zexuan
